#!/bin/bash

# Potree Converter Docker Setup Script
# This script helps you get started with the Potree converter

echo "=== Potree Converter Docker Setup ==="
echo ""

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "   Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    echo "   Visit: https://docs.docker.com/compose/install/"
    exit 1
fi

echo "✅ Docker and Docker Compose are installed"
echo ""

# Create directories if they don't exist
echo "📁 Creating required directories..."
mkdir -p las_laz output config
echo "   - las_laz/ (for input LAS/LAZ files)"
echo "   - output/ (for converted Potree files)"
echo "   - config/ (for configuration files)"
echo ""

# Check for LAS/LAZ files
las_count=$(find las_laz -name "*.las" -o -name "*.laz" 2>/dev/null | wc -l)
echo "📊 Found $las_count LAS/LAZ files in las_laz/ folder"

if [ $las_count -eq 0 ]; then
    echo ""
    echo "⚠️  No LAS/LAZ files found in the las_laz/ folder."
    echo "   Please add your point cloud files (.las or .laz) to the las_laz/ folder before running the converter."
    echo ""
    echo "   Example:"
    echo "   cp /path/to/your/pointcloud.las las_laz/"
    echo ""
fi

echo "🚀 Available commands:"
echo ""
echo "1. Build and run converter (automatic mode):"
echo "   docker-compose up --build"
echo ""
echo "2. Run with web viewer:"
echo "   docker-compose --profile viewer up --build"
echo "   Then open http://localhost:8080"
echo ""
echo "3. Interactive mode:"
echo "   docker-compose run --rm potree-converter bash"
echo ""
echo "4. View logs:"
echo "   docker-compose logs potree-converter"
echo ""
echo "5. Stop services:"
echo "   docker-compose down"
echo ""

if [ $las_count -gt 0 ]; then
    echo "✅ Ready to convert! Run one of the commands above."
else
    echo "📝 Next steps:"
    echo "   1. Add LAS/LAZ files to the las_laz/ folder"
    echo "   2. Run: docker-compose up --build"
fi

echo ""
echo "📖 For more information, see README.md"
