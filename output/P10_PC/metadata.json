{"version": "2.0", "name": "P10_PC", "description": "", "points": 2479315, "projection": "", "hierarchy": {"firstChunkSize": 5280, "stepSize": 4, "depth": 6}, "offset": [753808.17, 1502938.51, -1.230000000000004], "scale": [0.01, 0.01, 0.01], "spacing": 2.3094531250008004, "boundingBox": {"min": [753808.17, 1502938.51, -1.230000000000004], "max": [754103.7800000001, 1503234.12, 294.3800000001024]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [753808.17, 1502938.51, -1.229999999999998], "max": [754090.41, 1503234.12, 23.67], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [19843, 0, 2456493, 0, 0, 0, 0, 2979, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [7], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [1799, 4369, 257], "max": [64507, 61166, 58596], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "confidence", "description": "confidence values", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [5], "max": [38], "scale": [1], "offset": [0]}, {"name": "normal x", "description": "X surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal y", "description": "Y surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal z", "description": "Z surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-102], "max": [126], "scale": [0.007874015748031496], "offset": [0]}]}