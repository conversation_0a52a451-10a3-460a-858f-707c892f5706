{"version": "2.0", "name": "P18_PC", "description": "", "points": 920715, "projection": "", "hierarchy": {"firstChunkSize": 3652, "stepSize": 4, "depth": 5}, "offset": [752196.21, 1499400.5, -11.539999999999992], "scale": [0.01, 0.01, 0.01], "spacing": 2.1939062500005093, "boundingBox": {"min": [752196.21, 1499400.5, -11.539999999999992], "max": [752477.03, 1499681.32, 269.28000000006523]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [752196.21, 1499400.5, -11.539999999999997], "max": [752433.05, 1499681.32, 19.160000000000004], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [10986, 0, 906379, 0, 0, 0, 0, 3350, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [7], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [1028, 4112, 257], "max": [65535, 65535, 65535], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "confidence", "description": "confidence values", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [5], "max": [31], "scale": [1], "offset": [0]}, {"name": "normal x", "description": "X surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal y", "description": "Y surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal z", "description": "Z surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-124], "max": [126], "scale": [0.007874015748031496], "offset": [0]}]}