{"version": "2.0", "name": "P6_PC", "description": "", "points": 1112831, "projection": "", "hierarchy": {"firstChunkSize": 3498, "stepSize": 4, "depth": 6}, "offset": [751168.23, 1504483.4, -25.03], "scale": [0.01, 0.01, 0.01], "spacing": 2.4591406250001455, "boundingBox": {"min": [751168.23, 1504483.4, -25.03], "max": [751483, 1504798.17, 289.74000000001865]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [751168.23, 1504483.4, -25.029999999999998], "max": [751482.77, 1504798.17, 13.300000000000002], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [9509, 0, 1101816, 0, 0, 0, 0, 1506, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [7], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [771, 3341, 0], "max": [65535, 65535, 65535], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "confidence", "description": "confidence values", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [5], "max": [30], "scale": [1], "offset": [0]}, {"name": "normal x", "description": "X surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal y", "description": "Y surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal z", "description": "Z surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-98], "max": [126], "scale": [0.007874015748031496], "offset": [0]}]}