{"version": "2.0", "name": "P13_PC", "description": "", "points": 2942610, "projection": "", "hierarchy": {"firstChunkSize": 4026, "stepSize": 4, "depth": 6}, "offset": [754243.54, 1503771.21, -9.679999999999993], "scale": [0.01, 0.01, 0.01], "spacing": 3.1189843749998545, "boundingBox": {"min": [754243.54, 1503771.21, -9.679999999999993], "max": [754642.77, 1504170.44, 389.54999999998137]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [754243.54, 1503771.21, -9.679999999999998], "max": [754642.77, 1504168.32, 17.62], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [50206, 0, 2891840, 0, 0, 0, 0, 564, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [7], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [771, 2827, 0], "max": [63993, 60652, 56283], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "confidence", "description": "confidence values", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [5], "max": [62], "scale": [1], "offset": [0]}, {"name": "normal x", "description": "X surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal y", "description": "Y surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal z", "description": "Z surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-104], "max": [126], "scale": [0.007874015748031496], "offset": [0]}]}