{"version": "2.0", "name": "P17_PC", "description": "", "points": 1852611, "projection": "", "hierarchy": {"firstChunkSize": 4378, "stepSize": 4, "depth": 6}, "offset": [750904.8, 1500234.29, -22.739999999999995], "scale": [0.01, 0.01, 0.01], "spacing": 2.5474218750005093, "boundingBox": {"min": [750904.8, 1500234.29, -22.739999999999995], "max": [751230.8700000001, 1500560.36, 303.3300000000652]}, "encoding": "DEFAULT", "attributes": [{"name": "position", "description": "", "size": 12, "numElements": 3, "elementSize": 4, "type": "int32", "min": [750904.8, 1500234.29, -22.74], "max": [751225.48, 1500560.36, 13.590000000000002], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "intensity", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "return number", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "number of returns", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "classification", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "histogram": [35165, 0, 1815699, 0, 0, 0, 0, 1747, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], "min": [0], "max": [7], "scale": [1], "offset": [0]}, {"name": "scan angle rank", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "user data", "description": "", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [0], "max": [0], "scale": [1], "offset": [0]}, {"name": "point source id", "description": "", "size": 2, "numElements": 1, "elementSize": 2, "type": "uint16", "min": [1], "max": [1], "scale": [1], "offset": [0]}, {"name": "rgb", "description": "", "size": 6, "numElements": 3, "elementSize": 2, "type": "uint16", "min": [771, 3341, 0], "max": [65535, 65535, 65535], "scale": [1, 1, 1], "offset": [0, 0, 0]}, {"name": "confidence", "description": "confidence values", "size": 1, "numElements": 1, "elementSize": 1, "type": "uint8", "min": [5], "max": [58], "scale": [1], "offset": [0]}, {"name": "normal x", "description": "X surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal y", "description": "Y surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-126], "max": [126], "scale": [0.007874015748031496], "offset": [0]}, {"name": "normal z", "description": "Z surface normal", "size": 1, "numElements": 1, "elementSize": 1, "type": "int8", "min": [-109], "max": [126], "scale": [0.007874015748031496], "offset": [0]}]}