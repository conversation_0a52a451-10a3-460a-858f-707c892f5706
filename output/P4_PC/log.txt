INFO(chunker_countsort_laszip.cpp:176): counting P4_PC.las, first point: 0, num points: 1'000'000
INFO(chunker_countsort_laszip.cpp:176): counting P4_PC.las, first point: 1'000'000, num points: 374'990
INFO(indexer.cpp:1656): start indexing chunk r00
filesize: 5'921
min: 101.3012832, 13.6024577, -33.299999999999997
max: 110.7062832, 23.007457700000003, -23.894999999999996
INFO(indexer.cpp:1656): start indexing chunk r010
filesize: 4'340
min: 101.3012832, 13.6024577, -23.894999999999996
max: 106.0037832, 18.304957700000003, -19.192499999999995
INFO(indexer.cpp:1699): finished indexing chunk r00
INFO(indexer.cpp:1699): finished indexing chunk r010
INFO(indexer.cpp:1656): start indexing chunk r0110
filesize: 352'594
min: 101.3012832, 13.6024577, -19.192499999999995
max: 103.65253319999999, 15.953707700000002, -16.841249999999995
INFO(indexer.cpp:1656): start indexing chunk r011100
filesize: 1'011'654
min: 101.3012832, 13.6024577, -16.841249999999995
max: 101.8890957, 14.190270200000001, -16.253437499999997
INFO(indexer.cpp:1656): start indexing chunk r0111010
filesize: 1'627'314
min: 101.3012832, 13.6024577, -16.253437499999997
max: 101.59518944999999, 13.896363950000001, -15.959531249999996
INFO(indexer.cpp:1656): start indexing chunk r0111011
filesize: 2'757'729
min: 101.3012832, 13.6024577, -15.959531249999996
max: 101.59518944999999, 13.896363950000001, -15.665624999999995
INFO(indexer.cpp:1656): start indexing chunk r0111100
filesize: 1'780'268
min: 101.3012832, 13.6024577, -15.665624999999995
max: 101.59518944999999, 13.896363950000001, -15.371718749999996
INFO(indexer.cpp:1656): start indexing chunk r0111101
filesize: 746'387
min: 101.3012832, 13.6024577, -15.371718749999996
max: 101.59518944999999, 13.896363950000001, -15.077812499999995
INFO(indexer.cpp:1656): start indexing chunk r011111
filesize: 1'440'322
min: 101.3012832, 13.6024577, -15.077812499999995
max: 101.8890957, 14.190270200000001, -14.489999999999995
INFO(indexer.cpp:1656): start indexing chunk r1000000
filesize: 1'803'456
min: 101.3012832, 13.6024577, -14.489999999999995
max: 101.59518944999999, 13.896363950000001, -14.196093749999996
INFO(indexer.cpp:1656): start indexing chunk r1000010
filesize: 5'666'552
min: 101.3012832, 13.6024577, -13.902187499999995
max: 101.59518944999999, 13.896363950000001, -13.608281249999994
INFO(indexer.cpp:1656): start indexing chunk r1000001
filesize: 11'980'756
min: 101.3012832, 13.6024577, -14.196093749999996
max: 101.59518944999999, 13.896363950000001, -13.902187499999995
INFO(indexer.cpp:1656): start indexing chunk r1000011
filesize: 2'534'157
min: 101.3012832, 13.6024577, -13.608281249999994
max: 101.59518944999999, 13.896363950000001, -13.314374999999995
INFO(indexer.cpp:1656): start indexing chunk r1000100
filesize: 1'515'869
min: 101.3012832, 13.6024577, -13.314374999999995
max: 101.59518944999999, 13.896363950000001, -13.020468749999996
INFO(indexer.cpp:1699): finished indexing chunk r0110
INFO(indexer.cpp:1656): start indexing chunk r1000101
filesize: 1'180'077
min: 101.3012832, 13.6024577, -13.020468749999996
max: 101.59518944999999, 13.896363950000001, -12.726562499999995
INFO(indexer.cpp:1699): finished indexing chunk r011100
INFO(indexer.cpp:1656): start indexing chunk r100011
filesize: 946'926
min: 101.3012832, 13.6024577, -12.726562499999995
max: 101.8890957, 14.190270200000001, -12.138749999999995
INFO(indexer.cpp:1699): finished indexing chunk r0111101
INFO(indexer.cpp:1656): start indexing chunk r10010
filesize: 1'642'318
min: 101.3012832, 13.6024577, -12.138749999999995
max: 102.4769082, 14.778082700000002, -10.963124999999994
INFO(indexer.cpp:1699): finished indexing chunk r0111010
INFO(indexer.cpp:1656): start indexing chunk r10011
filesize: 1'592'873
min: 101.3012832, 13.6024577, -10.963124999999994
max: 102.4769082, 14.778082700000002, -9.7874999999999943
INFO(indexer.cpp:1699): finished indexing chunk r0111100
INFO(indexer.cpp:1656): start indexing chunk r10100
filesize: 1'389'389
min: 101.3012832, 13.6024577, -9.7874999999999943
max: 102.4769082, 14.778082700000002, -8.6118749999999942
INFO(indexer.cpp:1699): finished indexing chunk r1000101
INFO(indexer.cpp:1699): finished indexing chunk r011111
INFO(indexer.cpp:1699): finished indexing chunk r1000000
INFO(indexer.cpp:1699): finished indexing chunk r100011
INFO(indexer.cpp:1656): start indexing chunk r10101
filesize: 947'639
min: 101.3012832, 13.6024577, -8.6118749999999942
max: 102.4769082, 14.778082700000002, -7.436249999999994
INFO(indexer.cpp:1656): start indexing chunk r1011
filesize: 912'144
min: 101.3012832, 13.6024577, -7.436249999999994
max: 103.65253319999999, 15.953707700000002, -5.0849999999999937
INFO(indexer.cpp:1656): start indexing chunk r11
filesize: 786'005
min: 101.3012832, 13.6024577, -5.0849999999999937
max: 110.7062832, 23.007457700000003, 4.3200000000000074
INFO(indexer.cpp:1699): finished indexing chunk r0111011
INFO(indexer.cpp:1699): finished indexing chunk r1000100
INFO(indexer.cpp:1699): finished indexing chunk r10010
INFO(indexer.cpp:1699): finished indexing chunk r1000011
INFO(indexer.cpp:1699): finished indexing chunk r10011
INFO(indexer.cpp:1699): finished indexing chunk r10100
INFO(indexer.cpp:1699): finished indexing chunk r1011
INFO(indexer.cpp:1699): finished indexing chunk r10101
INFO(indexer.cpp:1699): finished indexing chunk r11
INFO(indexer.cpp:1699): finished indexing chunk r1000010
INFO(indexer.cpp:1699): finished indexing chunk r1000001
