#!/bin/bash

# Setup script for Potree viewer
echo "🔧 Setting up Potree viewer..."

# Create viewer directory structure
mkdir -p viewer/libs viewer/build

# Download Potree viewer files
echo "📥 Downloading Potree viewer..."
cd viewer

# Download Potree from GitHub releases
POTREE_VERSION="1.8.1"
wget -q "https://github.com/potree/potree/releases/download/${POTREE_VERSION}/potree.zip" -O potree.zip

if [ $? -eq 0 ]; then
    echo "✅ Downloaded Potree viewer"
    unzip -q potree.zip
    
    # Move files to correct locations
    if [ -d "potree" ]; then
        cp -r potree/libs ./
        cp -r potree/build ./
        rm -rf potree potree.zip
        echo "✅ Potree viewer setup complete"
    else
        echo "❌ Error: Potree directory not found in zip"
    fi
else
    echo "❌ Failed to download Potree viewer"
    echo "📝 Manual setup required:"
    echo "   1. Download Potree from https://github.com/potree/potree/releases"
    echo "   2. Extract to viewer/ directory"
    echo "   3. Ensure libs/ and build/ folders are in viewer/"
fi

cd ..

echo ""
echo "🎯 Setup complete! You can now:"
echo "   1. Add LAS/LAZ files to las_laz/ folder"
echo "   2. Run: docker-compose up --build"
echo "   3. Convert files with the converter"
echo "   4. Start viewer: docker-compose --profile viewer up"
echo "   5. Access: http://localhost:8080/{pointcloud_name}"
