#!/bin/bash

# Potree Converter Script
# This script processes LAS/LAZ files in the mounted volume

set -e

echo "=== Potree Converter Docker Container ==="
echo "Input directory: /app/las_laz"
echo "Output directory: /app/output"
echo ""

# Check if input directory exists and has files
if [ ! -d "/app/las_laz" ]; then
    echo "Error: Input directory /app/las_laz not found!"
    echo "Make sure you have mounted the las_laz volume correctly."
    exit 1
fi

# Count LAS/LAZ files
las_count=$(find /app/las_laz -name "*.las" -o -name "*.laz" | wc -l)
echo "Found $las_count LAS/LAZ files to process"

if [ $las_count -eq 0 ]; then
    echo "No LAS/LAZ files found in /app/las_laz"
    echo "Please add some .las or .laz files to the las_laz folder and restart the container."
    echo ""
    echo "Keeping container running for manual operations..."
    tail -f /dev/null
fi

# Create output directory if it doesn't exist
mkdir -p /app/output

# Set default values for environment variables
SPACING=${SPACING:-"auto"}
LEVELS=${LEVELS:-"5"}
OUTPUT_ATTRIBUTES=${OUTPUT_ATTRIBUTES:-"RGB,INTENSITY,CLASSIFICATION"}
OUTPUT_FORMAT=${OUTPUT_FORMAT:-"potree"}

echo "Conversion settings:"
echo "  Spacing: $SPACING"
echo "  Levels: $LEVELS"
echo "  Output Attributes: $OUTPUT_ATTRIBUTES"
echo "  Output Format: $OUTPUT_FORMAT"
echo ""

# Process each LAS/LAZ file
for file in /app/las_laz/*.las /app/las_laz/*.laz; do
    if [ -f "$file" ]; then
        filename=$(basename "$file")
        name_without_ext="${filename%.*}"
        output_dir="/app/output/$name_without_ext"
        
        echo "Processing: $filename"
        echo "Output directory: $output_dir"
        
        # Create output directory for this file
        mkdir -p "$output_dir"
        
        # Run PotreeConverter
        if [ "$SPACING" = "auto" ]; then
            PotreeConverter "$file" -o "$output_dir" --levels $LEVELS --output-attributes $OUTPUT_ATTRIBUTES
        else
            PotreeConverter "$file" -o "$output_dir" --spacing $SPACING --levels $LEVELS --output-attributes $OUTPUT_ATTRIBUTES
        fi
        
        echo "Completed: $filename"
        echo "---"
    fi
done

echo ""
echo "=== Conversion Complete ==="
echo "Converted files are available in /app/output"
echo "You can view them using a web server or the Potree viewer."
echo ""

# If CONVERT_ALL is not set to true, keep container running
if [ "$CONVERT_ALL" != "true" ]; then
    echo "Keeping container running for additional operations..."
    echo "You can exec into the container to run manual conversions:"
    echo "  docker exec -it potree-converter bash"
    tail -f /dev/null
fi
