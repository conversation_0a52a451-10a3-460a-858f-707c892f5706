# Use Ubuntu 22.04 as base image
FROM ubuntu:22.04

# Set environment variables to avoid interactive prompts
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    unzip \
    libboost-all-dev \
    libeigen3-dev \
    libflann-dev \
    libvtk9-dev \
    libqhull-dev \
    libusb-1.0-0-dev \
    libgtest-dev \
    libopenni-dev \
    libopenni2-dev \
    libpcap-dev \
    libpng-dev \
    libvtk9-qt-dev \
    python3 \
    python3-pip \
    nodejs \
    npm \
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Clone and build PotreeConverter
RUN git clone https://github.com/potree/PotreeConverter.git && \
    cd PotreeConverter && \
    mkdir build && \
    cd build && \
    cmake .. && \
    make -j$(nproc)

# Create directories for input and output
RUN mkdir -p /app/input /app/output /app/las_laz

# Copy the built PotreeConverter to a more accessible location
RUN cp /app/PotreeConverter/build/PotreeConverter /usr/local/bin/

# Create a conversion script
COPY convert.sh /app/convert.sh
RUN chmod +x /app/convert.sh

# Set the working directory to where files will be mounted
WORKDIR /app

# Default command
CMD ["/app/convert.sh"]
