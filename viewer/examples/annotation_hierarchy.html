<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
	<link rel="stylesheet" type="text/css" href="../libs/Cesium/Widgets/CesiumWidget/CesiumWidget.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	<script src="../libs/Cesium/Cesium.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');">
			<div id="cesiumContainer" style="position: absolute; width: 100%; height: 100%; background-color:green"></div>
		</div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

		import * as THREE from "../libs/three.js/build/three.module.js";

		window.cesiumViewer = new Cesium.Viewer('cesiumContainer', {
			useDefaultRenderLoop: false,
			animation: false,
			baseLayerPicker : false,
			fullscreenButton: false, 
			geocoder: false,
			homeButton: false,
			infoBox: false,
			sceneModePicker: false,
			selectionIndicator: false,
			timeline: false,
			navigationHelpButton: false,
			imageryProvider : Cesium.createOpenStreetMapImageryProvider({url : 'https://a.tile.openstreetmap.org/'}),
			terrainShadows: Cesium.ShadowMode.DISABLED,
		});

		let cp = new Cesium.Cartesian3(4303414.154026048, 552161.235598733, 4660771.704035539);
		cesiumViewer.camera.setView({
			destination : cp,
			orientation: {
				heading : 10, 
				pitch : -Cesium.Math.PI_OVER_TWO * 0.5, 
				roll : 0.0 
			}
		});

		window.potreeViewer = new Potree.Viewer(document.getElementById("potree_render_area"), {
			useDefaultRenderLoop: false
		});
	
		potreeViewer.setEDLEnabled(true);
		potreeViewer.setFOV(60);
		potreeViewer.setPointBudget(3_000_000);
		potreeViewer.setBackground(null);
		
		potreeViewer.setDescription("");
		
		potreeViewer.loadGUI(() => {
			potreeViewer.setLanguage('en');
			$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_scene").next().show();
			potreeViewer.toggleSidebar();
		});
		
		// CA13
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/opentopography/CA13_1.4/cloud.js", "CA13", function(e){
			let pointcloud = e.pointcloud;
			let scene = potreeViewer.scene;
			let material = pointcloud.material;

			scene.addPointCloud(pointcloud);

			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;

			potreeViewer.scene.view.setView(
				[675036.45, 3850315.78, 65076.70],
				[692869.03, 3925774.14, 1581.51],
			);

			let pointcloudProjection = e.pointcloud.projection;
			let mapProjection = proj4.defs("WGS84");

			window.toMap = proj4(pointcloudProjection, mapProjection);
			window.toScene = proj4(mapProjection, pointcloudProjection);

			{ // ANNOTATIONS
				let aRoot = potreeViewer.scene.annotations;

				let aCA13 = new Potree.Annotation({
					title: "CA13",
					position: [675036.45, 3850315.78, 65076.70],
					cameraPosition: [675036.45, 3850315.78, 65076.70],
					cameraTarget: [692869.03, 3925774.14, 1581.51],
				});
				aRoot.add(aCA13);

				let aSanSimeon = new Potree.Annotation({
					title: "San Simeon",
					position: [664147.50, 3946008.73, 16.30],
					cameraPosition: [664941.80, 3943568.06, 1925.30],
					cameraTarget: [664147.50, 3946008.73, 16.30],
				});
				aCA13.add(aSanSimeon);

				let aHearstCastle = new Potree.Annotation({
					title: "Hearst Castle",
					position: [665744.56, 3950567.52, 500.48],
					cameraPosition: [665692.66, 3950521.65, 542.02],
					cameraTarget: [665744.56, 3950567.52, 500.48],
				});
				aCA13.add(aHearstCastle);

				let aMorroBay = new Potree.Annotation({
					title: "Morro Bay",
					position: [695483.33, 3916430.09, 25.75],
					cameraPosition: [694114.65, 3911176.26, 3402.33],
					cameraTarget: [695483.33, 3916430.09, 25.75],
				});
				aCA13.add(aMorroBay);

				let aMorroRock = new Potree.Annotation({
					title: "Morro Rock",
					position: [693729.66, 3916085.19, 90.35],
					cameraPosition: [693512.77, 3915375.61, 342.33],
					cameraTarget: [693729.66, 3916085.19, 90.35],
				});
				aMorroBay.add(aMorroRock);

				let aMorroBayMutualWaterCo = new Potree.Annotation({
					title: "Morro Bay Mutual Water Co",
					position: [694699.45, 3916425.75, 39.78],
					cameraPosition: [694377.64, 3916289.32, 218.40],
					cameraTarget: [694699.45, 3916425.75, 39.78],
				});
				aMorroBay.add(aMorroBayMutualWaterCo);

				let aLilaKeiserPark = new Potree.Annotation({
					title: "Lila Keiser Park",
					position: [694674.99, 3917070.49, 10.86],
					cameraPosition: [694452.59, 3916845.14, 298.64],
					cameraTarget: [694674.99, 3917070.49, 10.86],
				});
				aMorroBay.add(aLilaKeiserPark);

				let aSanLuisObispo = new Potree.Annotation({
					title: "San Luis Obispo",
					position: [712573.39, 3907588.33, 146.44],
					cameraPosition: [711158.29, 3907019.82, 1335.89],
					cameraTarget: [712573.39, 3907588.33, 146.44],
				});
				aCA13.add(aSanLuisObispo);

				let aLopezHill = new Potree.Annotation({
					title: "Lopez Hill",
					position: [728635.63, 3895761.56, 456.33],
					cameraPosition: [728277.24, 3895282.29, 821.51],
					cameraTarget: [728635.63, 3895761.56, 456.33],
				});
				aCA13.add(aLopezHill);

				let aWhaleRockReservoir = new Potree.Annotation({
					title: "Whale Rock Reservoir",
					position: [692845.46, 3925528.53, 140.91],
					cameraPosition: [693073.32, 3922354.02, 2154.17],
					cameraTarget: [692845.46, 3925528.53, 140.91],
				});
				aCA13.add(aWhaleRockReservoir);

			}

			{ // TREE RETURNS POI - ANNOTATION & VOLUME
				let aRoot = scene.annotations;

				let elTitle = $(`
				<span>
					Tree Returns:
					<img name="action_return_number" src="${Potree.resourcePath}/icons/return_number.svg" class="annotation-action-icon"/>
					<img name="action_rgb" src="${Potree.resourcePath}/icons/rgb.png" class="annotation-action-icon"/>
				</span>`);

				elTitle.find("img[name=action_return_number]").click( () => {
					event.stopPropagation();
					material.activeAttributeName = "return_number";
					material.pointSizeType = Potree.PointSizeType.FIXED;
					material.size = 5;
					potreeViewer.setClipTask(Potree.ClipTask.SHOW_INSIDE);
				});
				
				elTitle.find("img[name=action_rgb]").click( () => {
					event.stopPropagation();
					material.activeAttributeName = "rgba";
					material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
					material.size = 1;
					potreeViewer.setClipTask(Potree.ClipTask.HIGHLIGHT);
				});

				elTitle.toString = () => "Tree Returns";
				

				let aTreeReturns = new Potree.Annotation({
					title: elTitle,
					position: [675756.75, 3937590.94, 80.21],
					cameraPosition: [675715.78, 3937700.36, 115.95],
					cameraTarget: [675756.75, 3937590.94, 80.21],
				});
				aRoot.add(aTreeReturns);
				aTreeReturns.domElement.find(".annotation-action-icon:first").css("filter", "invert(1)");

				let volume = new Potree.BoxVolume();
				volume.position.set(675755.4039368022, 3937586.911614576, 85);
				volume.scale.set(119.87189835418388, 68.3925257233834, 51.757483718373265);
				volume.rotation.set(0, 0, 0.8819755090987993, "XYZ");
				volume.clip = true;
				volume.visible = false;
				volume.name = "Trees";
				scene.addVolume(volume);
			}

		});



		function loop(timestamp){
			requestAnimationFrame(loop);

			potreeViewer.update(potreeViewer.clock.getDelta(), timestamp);

			potreeViewer.render();

			if(window.toMap !== undefined){

				{
					let camera = potreeViewer.scene.getActiveCamera();

					let pPos = new THREE.Vector3(0, 0, 0).applyMatrix4(camera.matrixWorld);
					let pRight = new THREE.Vector3(600, 0, 0).applyMatrix4(camera.matrixWorld);
					let pUp = new THREE.Vector3(0, 600, 0).applyMatrix4(camera.matrixWorld);
					let pTarget = potreeViewer.scene.view.getPivot();

					let toCes = (pos) => {
						let xy = [pos.x, pos.y];
						let height = pos.z;
						let deg = toMap.forward(xy);
						let cPos = Cesium.Cartesian3.fromDegrees(...deg, height);

						return cPos;
					};

					let cPos = toCes(pPos);
					let cUpTarget = toCes(pUp);
					let cTarget = toCes(pTarget);

					let cDir = Cesium.Cartesian3.subtract(cTarget, cPos, new Cesium.Cartesian3());
					let cUp = Cesium.Cartesian3.subtract(cUpTarget, cPos, new Cesium.Cartesian3());

					cDir = Cesium.Cartesian3.normalize(cDir, new Cesium.Cartesian3());
					cUp = Cesium.Cartesian3.normalize(cUp, new Cesium.Cartesian3());

					cesiumViewer.camera.setView({
						destination : cPos,
						orientation : {
							direction : cDir,
							up : cUp
						}
					});
					
				}

				let aspect = potreeViewer.scene.getActiveCamera().aspect;
				if(aspect < 1){
					let fovy = Math.PI * (potreeViewer.scene.getActiveCamera().fov / 180);
					cesiumViewer.camera.frustum.fov = fovy;
				}else{
					let fovy = Math.PI * (potreeViewer.scene.getActiveCamera().fov / 180);
					let fovx = Math.atan(Math.tan(0.5 * fovy) * aspect) * 2
					cesiumViewer.camera.frustum.fov = fovx;
				}
				
			}

			cesiumViewer.render();
		}

		requestAnimationFrame(loop);


	</script>
	
	
  </body>
</html>
