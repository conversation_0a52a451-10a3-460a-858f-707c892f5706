<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
		<input id="tiffTest" type="button" value="tiff test" />
	</div>

	
	
	<script type="module">

		import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(false);
		viewer.setFOV(60);
		viewer.setPointBudget(1_000_000);
		viewer.setMinNodeSize(0);
		viewer.loadSettingsFromURL();
		viewer.setBackground("skybox");
		viewer.setShowBoundingBox(false);
		
		viewer.setDescription(`Heidentor point cloud courtesy of <a href="http://archpro.lbg.ac.at/" target="_blank">Ludwig Boltzmann Institute - Archeological Prospection and Virtual Archeology</a> (26M points)`);
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			$("#menu_tools").next().show();
			$("#menu_scene").next().show();
			//viewer.toggleSidebar();
		});
		
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/archpro/heidentor/cloud.js", "Heidentor", function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			e.pointcloud.position.z = 0;
			let material = e.pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			
			viewer.scene.view.setView(
				[13.856, -9.125, 14.563],
				[-3.548,  2.728,  6.140],
			);

		});


		{ // ground plane
			const texture = new THREE.TextureLoader().load(`${Potree.resourcePath}/textures/rectangle.png`);

			texture.wrapS = THREE.RepeatWrapping;
			texture.wrapT = THREE.RepeatWrapping;
			texture.repeat.x = 10;
			texture.repeat.y = 10;
			texture.anisotropy = 16;

			let geometry = new THREE.PlaneBufferGeometry(1, 1, 10, 10);
			let material = new THREE.MeshBasicMaterial();
			material.map = texture;
			material.needsUpdate = true;
			let plane = new THREE.Mesh(geometry, material);
			plane.scale.set(18, 18, 18);
			plane.position.set(-2, 3, 0);
			plane.material.color.setRGB(0.8, 0.8, 0.8);
			window.plane = plane;
			viewer.scene.scene.add(plane);
		}

		// { // spheres to indicate coordinate axes
		// 	let sg = new THREE.SphereGeometry(0.1, 8, 8);

		// 	{ // ORIGIN
		// 		let sm = new THREE.MeshBasicMaterial({color: 0xaaaaaa});
		// 		let sphere = new THREE.Mesh(sg, sm);
		// 		sphere.position.set(0, 0, 0);
		// 		viewer.scene.scene.add(sphere);
		// 	}

		// 	{ // X
		// 		let sm = new THREE.MeshBasicMaterial({color: 0xff0000});
		// 		let sphere = new THREE.Mesh(sg, sm);
		// 		sphere.position.set(2, 0, 0);
		// 		viewer.scene.scene.add(sphere);
		// 	}

			
		// 	{ // Y
		// 		let sm = new THREE.MeshBasicMaterial({color: 0x00ff00});
		// 		let sphere = new THREE.Mesh(sg, sm);
		// 		sphere.position.set(0, 2, 0);
		// 		viewer.scene.scene.add(sphere);
		// 	}

			
		// 	{ // Z
		// 		let sm = new THREE.MeshBasicMaterial({color: 0x0000ff});
		// 		let sphere = new THREE.Mesh(sg, sm);
		// 		sphere.position.set(0, 0, 2);
		// 		viewer.scene.scene.add(sphere);
		// 	}

		// 	{ // RANDOM SPHERES
		// 		for(let i = 0; i < 2000; i++){
		// 			let color = 
		// 				((Math.random() * 255) << 16) |
		// 				((Math.random() * 255) <<  8) |
		// 				((Math.random() * 255) <<  0);
		// 			let sm = new THREE.MeshBasicMaterial({color: color});
		// 			let sphere = new THREE.Mesh(sg, sm);
		// 			sphere.position.set(
		// 				100 * Math.random() - 50, 
		// 				100 * Math.random() - 50, 
		// 				2);
		// 			sphere.scale.set(3, 3, 3);
		// 			viewer.scene.scene.add(sphere);

		// 			sphere.frustumCulled = false;
		// 		}
		// 	}
			
		// }
		
	</script>
	
  </body>
</html>
