<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	
	
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>

	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

	import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(false);
		//viewer.setFOV(62.94372232435855);
		viewer.setFOV(60);
		viewer.setPointBudget(3_000_000);
		
		viewer.setDescription(`
			Use Potree.OrientedImageLoader.load(...) to populate the scene with captured images. 
			Clicking an image will move the scene camera towards the image and overlay it over the point cloud. 
			You can use the measurement tools while the scene camera is bound to an image. Zoom in and out of the image via the mouse wheel. 
			Go back to 3D mode by clicking the button on the bottom right. 

			Point cloud courtesy of <a href="http://www.helimap.ch" target="_blank">Helimap System SA</a>.
		`);
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_tools").next().show();
			$("#menu_scene").next().show();
			viewer.toggleSidebar();
		});
		
		//Potree.loadPointCloud("../pointclouds/C/dev/epalinges/epalinges/als_converted/cloud.js", "epalinges", e => {
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/helimap/epalinges/als_converted/cloud.js", "epalinges", e => {
			let scene = viewer.scene;
			let pointcloud = e.pointcloud;
			
			let material = pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.FIXED;
			material.shape = Potree.PointShape.SQUARE;
			
			scene.addPointCloud(pointcloud);
			
			scene.view.position.set(2542013.003, 1155469.994, 1233.310);
			scene.view.lookAt(2542003.981, 1156007.326, 838.965);
		});
		
	</script>


	<script type="module">

	import * as THREE from "../libs/three.js/build/three.module.js";

		const cameraParamsPath = "http://**********/mschuetz/potree/resources/pointclouds/helimap/epalinges/img_selected/IXM35_190522_nodistortion.xml";
		const imageParamsPath = "http://**********/mschuetz/potree/resources/pointclouds/helimap/epalinges/img_selected/Calib190522_MN95_NF2_cam_estim.txt";

		Potree.OrientedImageLoader.load(cameraParamsPath, imageParamsPath, viewer).then( images => {
			viewer.scene.addOrientedImages(images);
		});
	
	</script>
	
	
  </body>
</html>
