<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="utf-8">
	<meta name="description" content="">
	<meta name="author" content="">
	<meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
	<title>Potree Viewer</title>

	<link rel="stylesheet" type="text/css" href="../build/potree/potree.css">
	<link rel="stylesheet" type="text/css" href="../libs/jquery-ui/jquery-ui.min.css">
	<link rel="stylesheet" type="text/css" href="../libs/openlayers3/ol.css">
	<link rel="stylesheet" type="text/css" href="../libs/spectrum/spectrum.css">
	<link rel="stylesheet" type="text/css" href="../libs/jstree/themes/mixed/style.css">
</head>

<body>
	<script src="../libs/jquery/jquery-3.1.1.min.js"></script>
	<script src="../libs/spectrum/spectrum.js"></script>
	<script src="../libs/jquery-ui/jquery-ui.min.js"></script>
	<script src="../libs/other/BinaryHeap.js"></script>
	<script src="../libs/tween/tween.min.js"></script>
	<script src="../libs/d3/d3.js"></script>
	<script src="../libs/proj4/proj4.js"></script>
	<script src="../libs/openlayers3/ol.js"></script>
	<script src="../libs/i18next/i18next.js"></script>
	<script src="../libs/jstree/jstree.js"></script>
	<script src="../build/potree/potree.js"></script>
	<script src="../libs/plasio/js/laslaz.js"></script>
	
	<!-- INCLUDE ADDITIONAL DEPENDENCIES HERE -->
	<!-- INCLUDE SETTINGS HERE -->
	
	<div class="potree_container" style="position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; ">
		<div id="potree_render_area" style="background-image: url('../build/potree/resources/images/background.jpg');"></div>
		<div id="potree_sidebar_container"> </div>
	</div>
	
	<script type="module">

		import * as THREE from "../libs/three.js/build/three.module.js";
	
		window.viewer = new Potree.Viewer(document.getElementById("potree_render_area"));
		
		viewer.setEDLEnabled(true);
		viewer.setFOV(60);
		viewer.setPointBudget(1_000_000);
		viewer.setMinNodeSize(0);
		viewer.loadSettingsFromURL();
		
		viewer.setDescription("");
		
		viewer.loadGUI(() => {
			viewer.setLanguage('en');
			$("#menu_appearance").next().show();
			viewer.toggleSidebar();
		});
		
		let sphere = new THREE.Mesh(
			new THREE.SphereGeometry(0.5, 32, 32),
			new THREE.MeshNormalMaterial()
		);
		viewer.scene.scene.add(sphere);

		// Sigeom
		Potree.loadPointCloud("http://**********/mschuetz/potree/resources/pointclouds/archpro/heidentor/cloud.js", "Heidentor", function(e){
			viewer.scene.addPointCloud(e.pointcloud);
			e.pointcloud.position.z = 0;
			let material = e.pointcloud.material;
			material.size = 1;
			material.pointSizeType = Potree.PointSizeType.ADAPTIVE;
			material.uniforms.uShadowColor.value = [0.0, 0, 0];
			
			viewer.scene.view.position.set(-31.007, 18.245, 20.695);
			viewer.scene.view.lookAt(-5.288, 2.126, 6.048);

			{ // create Animation Path & make light follow it

				let path = [
					[-8.60, 13.67, 7.08],
					[-12.21, 4.01, 7.45],
					[-0.67, 4.55, 5.00],
					[5.70, -4.38, 11.13],
					[-7.75, -10.75, 14.78],
					[-23.75, -4.88, 8.68],
					[-21.78, 11.73, 11.22],
				].map(v => new THREE.Vector3(...v));

				let animationPath = new Potree.AnimationPath(path);
				animationPath.closed = true;

				{ // render the path
					let geometry = animationPath.getGeometry();
					let material = new THREE.LineBasicMaterial();
					let line = new THREE.Line(geometry, material, {closed: animationPath.closed});
					viewer.scene.scene.add(line);
				}

				{ // render the control points of the path
					for(let pos of path){
						let sg = new THREE.SphereGeometry(0.2, 32, 32);
						let sm = new THREE.MeshBasicMaterial({color: 0xff0000});
						let s = new THREE.Mesh(sg, sm); 
						s.position.copy(pos);
						viewer.scene.scene.add(s);
					}
				}

				{ // Animate from beginning to end with a speed of 10 meters per second
					let start = 0;
					let end = Infinity;
					let speed = 30; 
					let animation = animationPath.animate(start, end, speed, t => {
						animation.repeat = true;

						// t is a value between 0 and 1.
						// use getPoint(t) to map from t to the position on the animation path
						let point = animation.getPoint(t);
						sphere.position.copy(point);
					});
					window.animation = animation;
				}

				window.animationPath = animationPath;
			}

			
		});

		function pause(){
			animation.pause();
			//light.disableShadowUpdates = true;
		}

		function resume(){
			animation.resume();
			//light.disableShadowUpdates = false;
		}
		
	</script>
	
	
  </body>
</html>
