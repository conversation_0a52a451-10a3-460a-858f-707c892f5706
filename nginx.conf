events {
    worker_connections 1024;
}

http {
    include       /etc/nginx/mime.types;
    default_type  application/octet-stream;
    
    # Enable gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/json;

    server {
        listen 80;
        server_name localhost;
        
        # Serve static files
        location / {
            root /usr/share/nginx/html;
            index index.html;
            try_files $uri $uri/ =404;
            
            # Enable CORS for Potree viewer
            add_header Access-Control-Allow-Origin *;
            add_header Access-Control-Allow-Methods 'GET, POST, OPTIONS';
            add_header Access-Control-Allow-Headers 'DNT,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Range';
        }
        
        # Handle .bin files (Potree binary files)
        location ~* \.(bin)$ {
            root /usr/share/nginx/html;
            add_header Access-Control-Allow-Origin *;
            add_header Content-Type application/octet-stream;
        }
        
        # Handle .js files
        location ~* \.(js)$ {
            root /usr/share/nginx/html;
            add_header Access-Control-Allow-Origin *;
            add_header Content-Type application/javascript;
        }
        
        # Handle .json files
        location ~* \.(json)$ {
            root /usr/share/nginx/html;
            add_header Access-Control-Allow-Origin *;
            add_header Content-Type application/json;
        }
    }
}
